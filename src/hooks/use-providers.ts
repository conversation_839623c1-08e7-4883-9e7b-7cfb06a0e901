import { useQuery, useInfiniteQuery } from '@tanstack/react-query'
import {
  providersService,
  type Provider,
  type Model,
  type ProvidersResponse,
  type ModelsResponse
} from '@/services/providers-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for providers
export const providersKeys = {
  all: ['providers'] as const,
  list: (page?: number, pageSize?: number) => [...providersKeys.all, 'list', page, pageSize] as const,
  models: (providerId: string) => [...providersKeys.all, 'models', providerId] as const,
  modelsList: (providerId: string, page?: number, pageSize?: number) => [...providersKeys.models(providerId), 'list', page, pageSize] as const,
}

// Hook for fetching providers with pagination
export function useProviders(page: number = 1, pageSize: number = 20) {
  return useQuery<ProvidersResponse, AxiosError<ApiError>>({
    queryKey: providersKeys.list(page, pageSize),
    queryFn: () => providersService.getProviders(page, pageSize),
    retry: 1,
  })
}

// Hook for fetching providers with infinite scroll
export function useProvidersInfinite(pageSize: number = 20) {
  return useInfiniteQuery<ProvidersResponse, AxiosError<ApiError>>({
    queryKey: [...providersKeys.all, 'infinite', pageSize],
    queryFn: ({ pageParam = 1 }) => providersService.getProviders(pageParam as number, pageSize),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.pagination
      return currentPage < totalPages ? currentPage + 1 : undefined
    },
    initialPageParam: 1,
    retry: 1,
  })
}

// Hook for fetching models for a specific provider with pagination
export function useModels(providerId: string, page: number = 1, pageSize: number = 20) {
  return useQuery<ModelsResponse, AxiosError<ApiError>>({
    queryKey: providersKeys.modelsList(providerId, page, pageSize),
    queryFn: () => providersService.getModels(providerId, page, pageSize),
    enabled: !!providerId, // Only run if providerId is provided
    retry: 1,
  })
}

// Hook for fetching models with infinite scroll
export function useModelsInfinite(providerId: string, pageSize: number = 20) {
  return useInfiniteQuery<ModelsResponse, AxiosError<ApiError>>({
    queryKey: [...providersKeys.models(providerId), 'infinite', pageSize],
    queryFn: ({ pageParam = 1 }) => providersService.getModels(providerId, pageParam as number, pageSize),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.pagination
      return currentPage < totalPages ? currentPage + 1 : undefined
    },
    initialPageParam: 1,
    enabled: !!providerId, // Only run if providerId is provided
    retry: 1,
  })
}
