import axiosClient from '@/lib/axios-client'

// Define types for provider data
export type Provider = {
  id: string
  provider: string
  description: string
  baseUrl: string
  isActive: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
  modelCount: number
}

export type ProvidersResponse = {
  success: boolean
  message: string
  providers: Provider[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    pageSize: number
  }
}

// Define types for model data
export type Model = {
  id: string
  providerId: string
  model: string
  modelId: string
  description: string
  pricePerTokens: number
  maxTokens: number
  temperature: number
  providerType: string
  isActive: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
  provider: Provider
}

export type ModelsResponse = {
  success: boolean
  message: string
  models: Model[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    pageSize: number
  }
}

// Providers service with methods for different operations
export const providersService = {
  // Get all providers with pagination
  getProviders: async (page: number = 1, pageSize: number = 20): Promise<ProvidersResponse> => {
    const response = await axiosClient.get<ProvidersResponse>('/providers', {
      params: { page, pageSize }
    })
    console.log('Providers response:', response.data)
    return response.data
  },

  // Get models for a specific provider with pagination
  getModels: async (providerId: string, page: number = 1, pageSize: number = 20): Promise<ModelsResponse> => {
    const response = await axiosClient.get<ModelsResponse>(`/providers/${providerId}/models`, {
      params: { page, pageSize }
    })
    console.log('Models response:', response.data)
    return response.data
  }
}
