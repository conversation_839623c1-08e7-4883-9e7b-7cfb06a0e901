
import axiosClient from "@/lib/axios-client";

// Define types for MCP tool schema
export type MCPToolInputSchema = {
  type: string;
  properties: Record<string, {
    type: string;
    description: string;
    properties?: Record<string, any>;
  }>;
  required: string[];
};

export type MCPToolOutputSchema = {
  type: string;
  properties: Record<string, {
    type: string;
    description: string;
    format?: string;
  }>;
  required: string[];
};

export type MCPTool = {
  name: string;
  description: string;
  input_schema: MCPToolInputSchema;
  output_schema?: MCPToolOutputSchema;
  annotations: Record<string, unknown> | null;
};

export type MCPToolsConfig = {
  meta: Record<string, unknown> | null;
  nextCursor: string | number | null;
  tools: MCPTool[];
};

// Define types for MCP server URLs in agent response
export type AgentMCPServerUrl = {
  url: string;
  type: "sse" | "http";
};

// Define types for MCP server data in agent response
export type AgentMCPServer = {
  id: string;
  name: string;
  logo: string | null;
  description: string;
  owner_id: string;
  user_ids: string[] | null;
  owner_type: string;
  urls: AgentMCPServerUrl[] | null;
  git_url: string | null;
  git_branch: string | null;
  deployment_status: string;
  visibility: string;
  tags: string[] | null;
  status: string;
  created_at: string;
  updated_at: string;
  department: string;
  mcp_tools_config: MCPToolsConfig;
};

// Define types for workflow data in agent response
export type AgentWorkflow = {
  id: string;
  name: string;
  description: string;
  workflow_url: string;
  builder_url: string;
  start_nodes: Array<{
    field: string;
    type: string;
    transition_id: string;
  }>;
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  workflow_template_id: string | null;
  template_owner_id: string | null;
  url: string | null;
  is_imported: boolean;
  version: string;
  visibility: string;
  category: string | null;
  tags: Record<string, unknown> | null;
  status: string;
  is_changes_marketplace: boolean;
  created_at: string;
  updated_at: string;
};

// Define types for agent data based on the API response
export type Agent = {
  id: string;
  name: string;
  description: string;
  avatar: string;
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  template_id: string | null;
  template_owner_id: string | null;
  is_imported: boolean;
  is_bench_employee: boolean;
  is_changes_marketplace: boolean;
  agent_category: string;
  category: string;
  system_message: string;
  model_provider: string | null;
  model_name: string | null;
  model_api_key: string | null;
  temperature?: number;
  max_tokens?: number;
  workflow_ids: string[];
  mcp_server_ids: string[];
  agent_topic_type: string;
  subscriptions: string | null;
  visibility: string;
  tags: string[] | null;
  status: string;
  department: string;
  organization_id: string | null;
  tone: string;
  files: string[];
  urls: string[];
  ruh_credentials: boolean;
  created_at: string;
  updated_at: string;
  variables?: Array<{
    id: string;
    name: string;
    description: string;
    type: string;
    default_value: string;
    created_at: string;
    updated_at: string;
  }>;
  agent_capabilities?: {
    id: string;
    capabilities: Array<{
      title: string;
      description: string;
    }>;
    input_modes: string[];
    output_modes: string[];
    response_model: string[];
    created_at: string;
    updated_at: string;
  };
  example_prompts?: string[];
  is_a2a?: boolean;
  is_customizable?: boolean;
  // New fields for detailed MCP and workflow data
  mcps?: AgentMCPServer[];
  workflows?: AgentWorkflow[];
  is_updated?: boolean; // Added for marketplace update check
};

// Define the pagination metadata type
export type PaginationMetadata = {
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

// Define the paginated response type
export type PaginatedAgentResponse = {
  data: Agent[];
  metadata: PaginationMetadata;
};

// Define the single agent response type
export type AgentResponse = {
  success: boolean;
  message: string;
  agent: Agent;
};

// Define types for agent version data
export type AgentVersion = {
  id: string;
  agent_id: string; // Assuming agent_id instead of workflow_id
  version_number: string;
  name: string;
  description: string;
  // Add other agent-specific version fields if different from workflow
  avatar: string;
  system_message: string;
  model_provider: string | null;
  model_name: string | null;
  workflow_ids: string[];
  mcp_server_ids: string[];
  category: string; // or agent_category
  tags: string[] | null;
  version_notes: string; // This field is important for the versions tab
  status: string;
  is_customizable: boolean; // Assuming this exists for agents too
  created_at: string;
  is_current: boolean;
};

export type AgentVersionsResponse = {
  success: boolean;
  message: string;
  versions: AgentVersion[];
  total: number;
  page: number;
  total_pages: number;
  current_version_id: string; // Assuming this exists for agents too
};

// Define the create agent request type
export type CreateAgentRequest = {
  name: string;
  description: string;
  system_message: string;
  model_provider: string | null;
  model_name: string | null;
  model_api_key?: string | null;
  temperature?: number;
  max_tokens?: number;
  workflow_ids: string[];
  mcp_server_ids: string[];
  tone: string;
  department: string;
  category: string;
  agent_topic_type: string;
  visibility: string;
  status: string;
  ruh_credentials: boolean;
  avatar?: string | null;
  files?: string[]; // Array of file URLs
  urls?: string[]; // Array of website URLs
  variables?: Array<{
    name: string;
    description: string;
    type: string;
    default_value: string;
  }>;
  capabilities_data?: {
    capabilities: Array<{
      title: string;
      description: string;
    }>;
    input_modes: string[];
    output_modes: string[];
    response_model: string[];
  };
  example_prompts?: string[];
};

// Define filter parameters type
export type AgentFilterParams = {
  page?: number;
  page_size?: number;
  department?: string;
  status?: string | string[];
  visibility?: string | string[];
  search?: string;
};

// Agents service with methods for different operations
export const agentService = {
  // Get all agents for the current user with optional filters
  getUserAgents: async (params?: AgentFilterParams): Promise<PaginatedAgentResponse> => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add pagination parameters
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

      // Add filter parameters
      if (params?.department) queryParams.append('department', params.department);

      // Handle multiple status values
      if (params?.status) {
        if (Array.isArray(params.status)) {
          params.status.forEach(status => queryParams.append('status', status));
        } else {
          queryParams.append('status', params.status);
        }
      }

      // Handle multiple visibility values
      if (params?.visibility) {
        if (Array.isArray(params.visibility)) {
          params.visibility.forEach(visibility => queryParams.append('visibility', visibility));
        } else {
          queryParams.append('visibility', params.visibility);
        }
      }

      // Add search parameter
      if (params?.search) queryParams.append('search', params.search);

      // Build the URL with query parameters
      const url = `/agents${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      console.log('Fetching agents with URL:', url);

      // Make the API call
      const response = await axiosClient.get<PaginatedAgentResponse>(url);
      console.log('Agent API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching agents:', error);
      throw error;
    }
  },

  // Get a single agent by ID
  getAgentById: async (id: string): Promise<AgentResponse> => {
    try {
      // Make the API call
      const response = await axiosClient.get<AgentResponse>(`/agents/${id}`);
      console.log('Agent details response:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching agent with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new agent
  createAgent: async (data: CreateAgentRequest): Promise<Agent> => {
    try {
      // Make the API call
      const response = await axiosClient.post<{ success: boolean; message: string; data: Agent }>('/agents', data);
      console.log('Create agent response:', response.data);
      return response.data.data;
    } catch (error) {
      console.error('Error creating agent:', error);
      throw error;
    }
  },

  // Update an agent
  updateAgent: async (
    id: string,
    data: Partial<CreateAgentRequest>
  ): Promise<Agent> => {
    try {
      // Make the API call
      const response = await axiosClient.put<{ data: Agent }>(`/agents/${id}`, data);
      console.log('Update agent response:', response.data);
      return response.data.data;
    } catch (error) {
      console.error(`Error updating agent with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete an agent
  deleteAgent: async (id: string): Promise<void> => {
    try {
      // Make the API call
      await axiosClient.delete(`/agents/${id}`);
      console.log(`Deleted agent with ID: ${id}`);
    } catch (error) {
      console.error(`Error deleting agent with ID ${id}:`, error);
      throw error;
    }
  },

  // Get all versions for an agent
  getAgentVersions: async (
    agentId: string,
    params?: {
      page?: number;
      page_size?: number;
    }
  ): Promise<AgentVersionsResponse> => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.page_size)
        queryParams.append("page_size", params.page_size.toString());

      const url = `/agents/${agentId}/versions${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await axiosClient.get<AgentVersionsResponse>(url);
      console.log(
        `Agent versions for ID ${agentId} response:`,
        response.data
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching versions for agent with ID ${agentId}:`,
        error
      );
      throw error;
    }
  },

  // Create a new version for an agent and optionally publish to marketplace
  createVersionAndPublish: async (
    agentId: string,
    publishToMarketplace: boolean = false
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const queryParams = new URLSearchParams();
      if (publishToMarketplace) {
        queryParams.append("publish_to_marketplace", "true");
      }

      const url = `/agents/${agentId}/create-version-and-publish${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await axiosClient.post<{
        success: boolean;
        message: string;
      }>(url, {});

      console.log("Create agent version and publish response:", response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(
        `Error creating version and publishing agent ${agentId}:`,
        error
      );
      if (error && typeof error === "object" && "response" in error) {
        const axiosError = error as {
          response: { data: unknown; status: number };
        };
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
      throw error;
    }
  },
};
