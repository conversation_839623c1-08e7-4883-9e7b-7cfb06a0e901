import axiosClient from "@/lib/axios-client";

// Define types for update payloads
export type CoreDetailsUpdatePayload = {
  name?: string;
  description?: string;
  avatar?: string;
  system_message?: string;
  model_provider?: string | null;
  model_name?: string | null;
  model_api_key?: string | null;
  temperature?: number;
  max_tokens?: number;
  department?: string;
  tone?: string;
  category?: string;
  agent_topic_type?: string;
  ruh_credentials?: boolean;
  is_changes_marketplace?: boolean;
};

export type MCPServersUpdatePayload = {
  mcp_server_ids: string[];
};

export type WorkflowsUpdatePayload = {
  workflow_ids: string[];
};

export type SettingsUpdatePayload = {
  is_changes_marketplace: boolean;
};

export type CapabilitiesUpdatePayload = {
  capabilities: Array<{
    title: string;
    description: string;
  }>;
  input_modes: string[];
  output_modes: string[];
  response_model: string[];
};

export type KnowledgeUpdatePayload = {
  files: string[];
  urls: string[];
};

export type VariablesUpdatePayload = {
  variables: Array<{
    name: string;
    description: string;
    type: 'text' | 'number' | 'json';
    default_value: string | null;
  }>;
};

// Agent update service with methods for different sections
export const agentUpdateService = {
  // Update core details (foundation and core logic)
  updateCoreDetails: async (
    agentId: string,
    data: CoreDetailsUpdatePayload
  ): Promise<{ success: boolean; message: string }> => {
    try {
      console.log(`Making API call to /agents/${agentId}/core-details with data:`, data);
      const response = await axiosClient.patch<{ success: boolean; message: string }>(
        `/agents/${agentId}/core-details`,
        data
      );
      console.log('Update core details response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating core details for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },

  // Update MCP servers (capabilities)
  updateMCPServers: async (
    agentId: string,
    data: MCPServersUpdatePayload
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.put<{ success: boolean; message: string }>(
        `/agents/${agentId}/mcp-servers`,
        data
      );
      console.log('Update MCP servers response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating MCP servers for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },

  // Update workflows (automation)
  updateWorkflows: async (
    agentId: string,
    data: WorkflowsUpdatePayload
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.put<{ success: boolean; message: string }>(
        `/agents/${agentId}/workflows`,
        data
      );
      console.log('Update workflows response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating workflows for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },

  // Update settings
  updateSettings: async (
    agentId: string,
    data: SettingsUpdatePayload
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.patch<{ success: boolean; message: string }>(
        `/agents/${agentId}/settings`,
        data
      );
      console.log('Update settings response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating settings for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },

  // Toggle visibility
  toggleVisibility: async (
    agentId: string
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.post<{ success: boolean; message: string }>(
        `/agents/${agentId}/toggle-visibility`,
        {}
      );
      console.log('Toggle visibility response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error toggling visibility for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },

  // Update capabilities
  updateCapabilities: async (
    agentId: string,
    data: CapabilitiesUpdatePayload
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.patch<{ success: boolean; message: string }>(
        `/agents/${agentId}/capabilities`,
        data
      );
      console.log('Update capabilities response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating capabilities for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },

  // Update knowledge (files and URLs)
  updateKnowledge: async (
    agentId: string,
    data: KnowledgeUpdatePayload
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.patch<{ success: boolean; message: string }>(
        `/agents/${agentId}/knowledge`,
        data
      );
      console.log('Update knowledge response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating knowledge for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },

  // Update variables (configuration)
  updateVariables: async (
    agentId: string,
    data: VariablesUpdatePayload
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.patch<{ success: boolean; message: string }>(
        `/agents/${agentId}/variables`,
        data
      );
      console.log('Update variables response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating variables for agent ${agentId}:`, error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown; status: number } };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
      throw error;
    }
  },
};
