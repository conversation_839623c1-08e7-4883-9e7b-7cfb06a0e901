"use client";

import { useState, useEffect } from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  Form
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { AgentData } from "../agent-creation-wizard";
import { useForm } from "react-hook-form";
import { Wand, ChevronDown } from "lucide-react";
import { toast } from "sonner";
import { useOptimizePrompt } from "@/hooks/use-prompt";
import { convertMarkdownToText } from "@/services/prompt-service";
import { useProvidersInfinite, useModelsInfinite } from "@/hooks/use-providers";
import { Provider, Model } from "@/services/providers-service";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface AgentCoreLogicProps {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
}

export function AgentCoreLogic({ data, updateData }: AgentCoreLogicProps) {
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [isProviderOpen, setIsProviderOpen] = useState(false);
  const [isModelOpen, setIsModelOpen] = useState(false);

  // Fetch providers with infinite scroll
  const {
    data: providersData,
    fetchNextPage: fetchNextProviders,
    hasNextPage: hasNextProviders,
    isFetchingNextPage: isFetchingNextProviders,
    isLoading: isLoadingProviders,
  } = useProvidersInfinite(20);

  // Fetch models for selected provider with infinite scroll
  const {
    data: modelsData,
    fetchNextPage: fetchNextModels,
    hasNextPage: hasNextModels,
    isFetchingNextPage: isFetchingNextModels,
    isLoading: isLoadingModels,
  } = useModelsInfinite(selectedProvider?.id || "", 20);

  // Flatten providers and models data
  const providers = providersData?.pages.flatMap(page => page.providers) || [];
  const models = modelsData?.pages.flatMap(page => page.models) || [];

  // Initialize the optimize prompt mutation
  const optimizePromptMutation = useOptimizePrompt({
    onSuccess: (response) => {
      // Convert markdown response to plain text
      const convertedPrompt = convertMarkdownToText(response.improved_prompt);

      // Update the form and parent data with the converted prompt
      form.setValue("systemPrompt", convertedPrompt);
      updateData({ systemPrompt: convertedPrompt });
      toast.success("Prompt optimized successfully");
    },
    onError: (error) => {
      console.error('Error optimizing prompt:', error);
      const errorMessage = error.response?.data?.message || 'Failed to optimize prompt. Please try again.';
      toast.error(errorMessage);
    },
  });

  const form = useForm({
    defaultValues: {
      systemPrompt: data.systemPrompt ||
        `You are a helpful AI assistant called ${data.name || "[Agent Name]"}. ${data.description || ""}`,
      temperature: data.temperature || 0.7,
      maxTokens: data.maxTokens || 2048,
    }
  });

  // Effect to sync selected provider and model with existing data
  useEffect(() => {
    if (data.aiProvider && providers.length > 0) {
      const provider = providers.find(p => p.provider === data.aiProvider);
      if (provider) {
        setSelectedProvider(provider);
      }
    }
  }, [data.aiProvider, providers]);

  useEffect(() => {
    if (data.aiModel && models.length > 0) {
      const model = models.find(m => m.model === data.aiModel);
      if (model) {
        setSelectedModel(model);
      }
    }
  }, [data.aiModel, models]);

  const optimizePrompt = () => {
    // Get the current prompt value
    const currentPrompt = form.getValues("systemPrompt");

    // Validate that we have the required data
    if (!currentPrompt.trim()) {
      toast.error("Please enter a system prompt before optimizing");
      return;
    }

    if (!data.name?.trim() || !data.description?.trim()) {
      toast.error("Please complete the agent name and description in the Foundation step before optimizing the prompt");
      return;
    }

    // Call the API to optimize the prompt
    optimizePromptMutation.mutate({
      original_prompt: currentPrompt,
      agent_context: {
        title: data.name,
        description: data.description,
      },
    });
  };

  const handleProviderSelect = (provider: Provider) => {
    setSelectedProvider(provider);
    setSelectedModel(null); // Reset model when provider changes
    updateData({
      aiProvider: provider.provider,
      aiModel: undefined
    });
    setIsProviderOpen(false);
  };

  const handleModelSelect = (model: Model) => {
    setSelectedModel(model);
    updateData({ aiModel: model.model });
    setIsModelOpen(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">Agent Core Logic</h2>
        <p className="text-muted-foreground">
          Define how your agent thinks and responds
        </p>
      </div>

      <Form {...form}>
        <form className="space-y-6">
          <FormField
            control={form.control}
            name="systemPrompt"
            render={({ field }) => (
              <FormItem className="max-w-4xl">
                <div className="flex items-center justify-between mb-2">
                  <FormLabel className="text-base">System Prompt</FormLabel>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-xs font-medium"
                    onClick={optimizePrompt}
                    disabled={optimizePromptMutation.isPending}
                  >
                    {optimizePromptMutation.isPending ? (
                      <>
                        <svg className="animate-spin h-3.5 w-3.5 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Optimizing...
                      </>
                    ) : (
                      <>
                        <Wand className="mr-1.5 h-3.5 w-3.5" />
                        Optimize Prompt
                      </>
                    )}
                  </Button>
                </div>
                <FormControl>
                  <Textarea
                    placeholder="You are a helpful assistant..."
                    className="min-h-[250px] max-h-[500px] font-mono text-sm resize-none"
                    maxLength={10000}
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      updateData({ systemPrompt: e.target.value });
                    }}
                  />
                </FormControl>
                <div className="flex justify-between mt-1.5">
                  <FormDescription>
                    This defines your agent&apos;s behavior, personality, and capabilities.
                  </FormDescription>
                  <span className="text-xs text-muted-foreground">
                    {field.value?.length || 0}/10000
                  </span>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* AI Provider and Model Selection */}
          <div className="space-y-6 max-w-4xl">
            <div className="grid gap-6 md:grid-cols-2">
              {/* AI Provider Dropdown */}
              <div className="space-y-2">
                <label className="text-base font-medium">AI Provider</label>
                <Popover open={isProviderOpen} onOpenChange={setIsProviderOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={isProviderOpen}
                      className="w-full justify-between"
                    >
                      {selectedProvider ? selectedProvider.provider : "Select a provider"}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-0" align="start">
                    <div className="max-h-60 overflow-y-auto">
                      {isLoadingProviders ? (
                        <div className="p-4 text-center text-sm text-muted-foreground">
                          Loading providers...
                        </div>
                      ) : providers.length > 0 ? (
                        <>
                          {providers.map((provider) => (
                            <div
                              key={provider.id}
                              className="flex items-center p-2 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer"
                              onClick={() => handleProviderSelect(provider)}
                            >
                              <span className="text-sm">{provider.provider}</span>
                              <span className="ml-auto text-xs text-muted-foreground">
                                {provider.modelCount} models
                              </span>
                            </div>
                          ))}
                          {hasNextProviders && (
                            <div className="p-2 border-t">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="w-full"
                                onClick={() => fetchNextProviders()}
                                disabled={isFetchingNextProviders}
                              >
                                {isFetchingNextProviders ? "Loading..." : "Load More"}
                              </Button>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="p-4 text-center text-sm text-muted-foreground">
                          No providers found
                        </div>
                      )}
                    </div>
                  </PopoverContent>
                </Popover>
                <p className="text-sm text-muted-foreground">
                  The AI provider that will power your agent
                </p>
              </div>

              {/* AI Model Dropdown */}
              <div className="space-y-2">
                <label className="text-base font-medium">AI Model</label>
                <Popover open={isModelOpen} onOpenChange={setIsModelOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={isModelOpen}
                      className="w-full justify-between"
                      disabled={!selectedProvider}
                    >
                      {selectedModel ? selectedModel.model : "Select a model"}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-0" align="start">
                    <div className="max-h-60 overflow-y-auto">
                      {isLoadingModels ? (
                        <div className="p-4 text-center text-sm text-muted-foreground">
                          Loading models...
                        </div>
                      ) : models.length > 0 ? (
                        <>
                          {models.map((model) => (
                            <div
                              key={model.id}
                              className="flex flex-col p-2 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer"
                              onClick={() => handleModelSelect(model)}
                            >
                              <span className="text-sm font-medium">{model.model}</span>
                              <span className="text-xs text-muted-foreground">
                                {model.description}
                              </span>
                            </div>
                          ))}
                          {hasNextModels && (
                            <div className="p-2 border-t">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="w-full"
                                onClick={() => fetchNextModels()}
                                disabled={isFetchingNextModels}
                              >
                                {isFetchingNextModels ? "Loading..." : "Load More"}
                              </Button>
                            </div>
                          )}
                        </>
                      ) : selectedProvider ? (
                        <div className="p-4 text-center text-sm text-muted-foreground">
                          No models found for this provider
                        </div>
                      ) : (
                        <div className="p-4 text-center text-sm text-muted-foreground">
                          Select a provider first
                        </div>
                      )}
                    </div>
                  </PopoverContent>
                </Popover>
                <p className="text-sm text-muted-foreground">
                  The specific model to use for this agent
                </p>
              </div>
            </div>

            {/* Temperature Slider */}
            <FormField
              control={form.control}
              name="temperature"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-base">Temperature</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-4">
                      <Slider
                        min={0}
                        max={2}
                        step={0.1}
                        value={[field.value]}
                        onValueChange={(value) => {
                          field.onChange(value[0]);
                          updateData({ temperature: value[0] });
                        }}
                        className="flex-1"
                      />
                      <div className="w-16 h-10 border border-input rounded-md flex items-center justify-center bg-background text-sm font-medium">
                        {field.value.toFixed(1)}
                      </div>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Higher values (0.7-1.0) make output more random, lower values (0.2-0.5) make it more deterministic
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Max Tokens Input */}
            <FormField
              control={form.control}
              name="maxTokens"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Max Tokens</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={100000}
                      placeholder="2048"
                      {...field}
                      onChange={(e) => {
                        const value = parseInt(e.target.value) || 0;
                        field.onChange(value);
                        updateData({ maxTokens: value });
                      }}
                      className="w-full"
                    />
                  </FormControl>
                  <FormDescription>
                    Maximum number of tokens the model can generate in response
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>
    </div>
  );
}
