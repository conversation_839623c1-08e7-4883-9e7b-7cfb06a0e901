"use client";

import { useState, useEffect } from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  Form
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { AgentData } from "../agent-creation-wizard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { Wand, ChevronDown } from "lucide-react";
import { toast } from "sonner";
import { useOptimizePrompt } from "@/hooks/use-prompt";
import { convertMarkdownToText } from "@/services/prompt-service";
import { useProvidersInfinite, useModelsInfinite } from "@/hooks/use-providers";
import { Provider, Model } from "@/services/providers-service";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface AgentCoreLogicProps {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
}

// Sample AI providers and models
const aiProviders = [
  {
    name: "OpenAI",
    models: ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"]
  },
  {
    name: "Anthropic",
    models: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"]
  },
  {
    name: "META",
    models: ["gemini-pro", "gemini-ultra"]
  },
  {
    name: "Mistral AI",
    models: ["mistral-large", "mistral-medium", "mistral-small"]
  }
];

export function AgentCoreLogic({ data, updateData }: AgentCoreLogicProps) {
  const [provider, setProvider] = useState(data.aiProvider);

  // Initialize the optimize prompt mutation
  const optimizePromptMutation = useOptimizePrompt({
    onSuccess: (response) => {
      // Convert markdown response to plain text
      const convertedPrompt = convertMarkdownToText(response.improved_prompt);

      // Update the form and parent data with the converted prompt
      form.setValue("systemPrompt", convertedPrompt);
      updateData({ systemPrompt: convertedPrompt });
      toast.success("Prompt optimized successfully");
    },
    onError: (error) => {
      console.error('Error optimizing prompt:', error);
      const errorMessage = error.response?.data?.message || 'Failed to optimize prompt. Please try again.';
      toast.error(errorMessage);
    },
  });

  const form = useForm({
    defaultValues: {
      systemPrompt: data.systemPrompt ||
        `You are a helpful AI assistant called ${data.name || "[Agent Name]"}. ${data.description || ""}`,
      aiProvider: data.aiProvider,
      aiModel: data.aiModel,
      apiKeyId: data.apiKeyId,
      ruhCredentials: data.ruhCredentials || false
    }
  });

  const getModelsForProvider = (providerName: string) => {
    const provider = aiProviders.find(p => p.name === providerName);
    return provider ? provider.models : [];
  };

  const optimizePrompt = () => {
    // Get the current prompt value
    const currentPrompt = form.getValues("systemPrompt");

    // Validate that we have the required data
    if (!currentPrompt.trim()) {
      toast.error("Please enter a system prompt before optimizing");
      return;
    }

    if (!data.name?.trim() || !data.description?.trim()) {
      toast.error("Please complete the agent name and description in the Foundation step before optimizing the prompt");
      return;
    }

    // Call the API to optimize the prompt
    optimizePromptMutation.mutate({
      original_prompt: currentPrompt,
      agent_context: {
        title: data.name,
        description: data.description,
      },
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">Agent Core Logic</h2>
        <p className="text-muted-foreground">
          Define how your agent thinks and responds
        </p>
      </div>

      <Form {...form}>
        <form className="space-y-6">
          <FormField
            control={form.control}
            name="systemPrompt"
            render={({ field }) => (
              <FormItem className="max-w-4xl">
                <div className="flex items-center justify-between mb-2">
                  <FormLabel className="text-base">System Prompt</FormLabel>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-xs font-medium"
                    onClick={optimizePrompt}
                    disabled={optimizePromptMutation.isPending}
                  >
                    {optimizePromptMutation.isPending ? (
                      <>
                        <svg className="animate-spin h-3.5 w-3.5 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Optimizing...
                      </>
                    ) : (
                      <>
                        <Wand className="mr-1.5 h-3.5 w-3.5" />
                        Optimize Prompt
                      </>
                    )}
                  </Button>
                </div>
                <FormControl>
                  <Textarea
                    placeholder="You are a helpful assistant..."
                    className="min-h-[250px] max-h-[500px] font-mono text-sm resize-none"
                    maxLength={10000}
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      updateData({ systemPrompt: e.target.value });
                    }}
                  />
                </FormControl>
                <div className="flex justify-between mt-1.5">
                  <FormDescription>
                    This defines your agent&apos;s behavior, personality, and capabilities.
                  </FormDescription>
                  <span className="text-xs text-muted-foreground">
                    {field.value?.length || 0}/10000
                  </span>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* RUH Credentials Toggle */}
          <FormField
            control={form.control}
            name="ruhCredentials"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 max-w-4xl">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    Use RUH Pre Configuration
                  </FormLabel>
                  <FormDescription>
                    When enabled, RUH AI will use optimized LLM settings. Turn off to use custom settings.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked);
                      if (checked) {
                        // When RUH credentials is enabled, clear custom settings
                        updateData({
                          ruhCredentials: checked,
                          aiProvider: undefined,
                          aiModel: undefined,
                          apiKeyId: null
                        });
                      } else {
                        updateData({ ruhCredentials: checked });
                      }
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* AI Provider and Model Selection - Only show when RUH credentials is disabled */}
          {!form.watch("ruhCredentials") && (
            <div className="space-y-6 max-w-4xl">
              <div className="grid gap-6 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="aiProvider"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">AI Provider</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          setProvider(value);
                          // Reset model when provider changes
                          const models = getModelsForProvider(value);
                          if (models.length > 0) {
                            form.setValue("aiModel", models[0]);
                            updateData({ aiProvider: value, aiModel: models[0] });
                          } else {
                            updateData({ aiProvider: value });
                          }
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a provider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {aiProviders.map(provider => (
                            <SelectItem key={provider.name} value={provider.name}>
                              {provider.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The AI provider that will power your agent
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="aiModel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">AI Model</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          updateData({ aiModel: value });
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a model" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {getModelsForProvider(provider || "").map(model => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The specific model to use for this agent
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* API Key Selection */}
              <FormField
                control={form.control}
                name="apiKeyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">API Key</FormLabel>
                    <FormControl>
                      <ApiKeySelector
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          updateData({ apiKeyId: value });
                        }}
                        placeholder="Select an API key"
                      />
                    </FormControl>
                    <FormDescription>
                      Choose the API key to use for this agent
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}
