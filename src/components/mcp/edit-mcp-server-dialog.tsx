"use client"

import { useState, useEffect } from "react"
import { useForm, FieldErrors } from "react-hook-form" // Import FieldErrors
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Globe, Github, X, FolderGit2, GitBranch, Edit, Trash2, Plus } from "lucide-react"
import { toast } from "sonner"
import { useUpdateMCPServer } from "@/hooks/use-mcp-servers"
import { FileUpload } from "@/components/common/file-upload"
import { useFieldArray } from "react-hook-form"

import { MCPServer } from "@/services/mcp-service"





// Schema for individual server configuration
const serverConfigItemSchema = z.object({
  url: z.string().optional(), // URL is optional at base level
  protocol: z.enum(["Streamable HTTP", "SSE", "STDIO"]),
}).superRefine((data, ctx) => {
  if ((data.protocol === "Streamable HTTP" || data.protocol === "SSE") && (!data.url || data.url.trim() === "")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "URL is required for Streamable HTTP and SSE protocols.",
      path: ["url"],
    });
  }
  // For STDIO, url can be undefined or empty, which is fine.
});

// Combined schema for TypeScript types
const mcpServerSchema = z.object({
  name: z.string().min(1, "Server name is required"),
  category: z.enum(["general", "sales", "marketing", "engineering", "finance", "hr"], {
    required_error: "Category is required",
  }),
  description: z.string().optional(),
  tags: z.string().optional(),
  logo: z.string().optional(),
  repositoryUrl: z.string().optional(),
  repository: z.string().optional(),
  branch: z.string().optional(),
  serverConfigs: z.array(serverConfigItemSchema).optional(),
  gitUrl: z.string().optional(),
  mcp_type: z.enum(["SSE", "Streamable HTTP", "STDIO"]).optional(),
  env_keys: z.array(z.object({
    key: z.string().min(1, "Key is required"),
    description: z.string().optional(),
  })).optional(),
  component_category: z.string().nullable().optional(),
})

type MCPServerFormData = z.infer<typeof mcpServerSchema>

interface EditMCPServerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  mcpServer: MCPServer
  onSuccess?: () => void
}

const componentCategories = [
  { value: "notifications alerts", label: "Notifications & Alerts" },
  { value: "communication", label: "Communication" },
  { value: "social media", label: "Social Media" },
  { value: "database", label: "Database" },
  { value: "cloud storage", label: "Cloud Storage" },
  { value: "devops system", label: "DevOps System" },
  { value: "file handling", label: "File Handling" },
]

export function EditMCPServerDialog({ open, onOpenChange, mcpServer, onSuccess }: EditMCPServerDialogProps) {
  // Determine which tab to show based on git_branch
  const hasGitBranch = mcpServer.git_branch && mcpServer.git_branch !== ""
  const [activeTab, setActiveTab] = useState(hasGitBranch ? "github" : "hosted")
  const [logoUrl, setLogoUrl] = useState<string>(mcpServer.logo || "")

  const updateMCPServer = useUpdateMCPServer()

  const form = useForm<MCPServerFormData>({
    resolver: zodResolver(mcpServerSchema),
    defaultValues: {
      name: mcpServer.name,
      category: mcpServer.category as "general" | "sales" | "marketing" | "engineering" | "finance" | "hr",
      description: mcpServer.description || "",
      tags: mcpServer.tags?.join(", ") || "",
      logo: mcpServer.logo || "",
      gitUrl: mcpServer.git_url || "",
      repository: "",
      branch: mcpServer.git_branch || "",
      serverConfigs: mcpServer.config?.map(config => ({
        url: config.url || "", // Ensure url is an empty string if undefined
        protocol: config.type === "streamable-http" ? "Streamable HTTP" : config.type.toUpperCase() as "SSE" | "STDIO"
      })) || [{ url: "", protocol: "Streamable HTTP" as const }],
      mcp_type: mcpServer.git_branch ? (mcpServer.config?.[0]?.type === "streamable-http" ? "Streamable HTTP" : mcpServer.config?.[0]?.type?.toUpperCase() || "SSE") as "Streamable HTTP" | "SSE" | "STDIO" : undefined,
      env_keys: mcpServer.env_keys || [],
      component_category: mcpServer.component_category || null,
    },
  })

  const { fields: envKeyFields, append: appendEnvKey, remove: removeEnvKey } = useFieldArray({
    control: form.control,
    name: "env_keys",
  })

  // Update form when mcpServer changes
  useEffect(() => {
    if (mcpServer) {
      form.reset({
        name: mcpServer.name,
        category: mcpServer.category as "general" | "sales" | "marketing" | "engineering" | "finance" | "hr",
        description: mcpServer.description || "",
        tags: mcpServer.tags?.join(", ") || "",
        logo: mcpServer.logo || "",
        gitUrl: mcpServer.git_url || "",
        repository: "",
        branch: mcpServer.git_branch || "",
        serverConfigs: mcpServer.config?.map(config => ({
          url: config.url || "", // Ensure url is an empty string if undefined
          protocol: config.type === "streamable-http" ? "Streamable HTTP" : config.type.toUpperCase() as "SSE" | "STDIO"
        })) || [{ url: "", protocol: "Streamable HTTP" as const }],
        mcp_type: mcpServer.git_branch ? (mcpServer.config?.[0]?.type === "streamable-http" ? "Streamable HTTP" : mcpServer.config?.[0]?.type?.toUpperCase() || "SSE") as "Streamable HTTP" | "SSE" | "STDIO" : undefined,
        env_keys: mcpServer.env_keys || [],
        component_category: mcpServer.component_category || null,
      })
      setLogoUrl(mcpServer.logo || "")

      // Set the correct tab based on git_branch
      const hasGitBranch = mcpServer.git_branch && mcpServer.git_branch !== ""
      setActiveTab(hasGitBranch ? "github" : "hosted")
    }
  }, [mcpServer, form])

  const handleLogoUpload = (urls: string[]) => {
    if (urls.length > 0) {
      setLogoUrl(urls[0])
      form.setValue("logo", urls[0])
    }
  }

  const handleRemoveLogo = () => {
    setLogoUrl("")
    form.setValue("logo", "")
  }

  const onSubmit = async (data: MCPServerFormData) => {
    try {
      // Prepare the payload with only changed basic fields
      console.log("Data:", data)
      const payload: any = {}

      // Check basic fields for changes
      if (data.name !== mcpServer.name) {
        payload.name = data.name
      }

      if ((data.description || "") !== (mcpServer.description || "")) {
        payload.description = data.description || ""
      }

      if (data.category !== mcpServer.category) {
        payload.category = data.category
      }

      // Check tags for changes
      const newTags = data.tags ? data.tags.split(",").map(tag => tag.trim()).filter(Boolean) : []
      const currentTags = mcpServer.tags || []
      if (JSON.stringify(newTags.sort()) !== JSON.stringify(currentTags.sort())) {
        payload.tags = newTags
      }

      // Check logo for changes
      if (logoUrl !== (mcpServer.logo || "")) {
        payload.logo = logoUrl
      }

      // For GitHub deployments, check mcp_type and env_keys changes
      if (activeTab === "github") {
        if (data.mcp_type !== (mcpServer.mcp_type?.toUpperCase() === "STREAMABLE-HTTP" ? "Streamable HTTP" : mcpServer.mcp_type?.toUpperCase() || "")) {
          payload.mcp_type = data.mcp_type?.toLowerCase() === "streamable http" ? "streamable-http" : data.mcp_type?.toLowerCase()
        }

        // Check env_keys for changes
        const newEnvKeys = data.env_keys || []
        const currentEnvKeys = mcpServer.env_keys || []
        if (JSON.stringify(newEnvKeys) !== JSON.stringify(currentEnvKeys)) {
          payload.env_keys = newEnvKeys
        }
      }

      // For hosted URLs only, check if serverConfigs have changed
      if (activeTab === "hosted") {
        const newConfigs = data.serverConfigs || []
        const currentConfigs = mcpServer.config || []

        // Deep comparison of URLs, order-agnostic
        const areConfigsEqual = newConfigs.length === currentConfigs.length &&
                             newConfigs.every(newConfig =>
                               currentConfigs.some(currentConfig =>
                                 currentConfig.url === newConfig.url &&
                                 currentConfig.type === (newConfig.protocol.toLowerCase() === "streamable http" ? "streamable-http" : newConfig.protocol.toLowerCase())
                               )
                             )

        if (!areConfigsEqual) {
          payload.config = newConfigs.map(serverConfig => ({
            url: serverConfig.url,
            type: serverConfig.protocol.toLowerCase() === "streamable http" ? "streamable-http" : serverConfig.protocol.toLowerCase() as "sse" | "stdio" | "streamable-http"
          }))
        }

        if (data.gitUrl !== (mcpServer.git_url || "")) {
          payload.git_url = data.gitUrl || ""
        }
      }

      if (
        (data.component_category || null) !== (mcpServer.component_category || null)
      ) {
        payload.component_category = data.component_category || null
      }

      // Only make API call if there are changes
      if (Object.keys(payload).length === 0) {
        toast.info("No changes detected")
        onOpenChange(false)
        return
      }

      console.log("Payload:", payload)
      await updateMCPServer.mutateAsync({
        id: mcpServer.id,
        data: payload
      })

      toast.success("MCP server updated successfully")
      onOpenChange(false)
      onSuccess?.()
    } catch (error) {
      console.error("Error updating MCP server:", error)
      toast.error("Failed to update MCP server")
    }
  }

  // New handler for validation errors
  const onInvalidSubmit = (errors: FieldErrors<MCPServerFormData>) => {
    console.error("Form validation errors:", errors)
    toast.error("Please fix the validation errors highlighted in the form.")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit MCP Server
          </DialogTitle>
          <DialogDescription>
            Update your MCP server configuration and deployment settings.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          {/* Pass both success and error handlers to handleSubmit */}
          <form onSubmit={form.handleSubmit(onSubmit, onInvalidSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Server Name</FormLabel>
                    <FormControl>
                      <Input placeholder="My MCP Server" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe what your MCP server does..."
                        className="resize-none"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="general">General</SelectItem>
                        <SelectItem value="sales">Sales</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                        <SelectItem value="engineering">Engineering</SelectItem>
                        <SelectItem value="finance">Finance</SelectItem>
                        <SelectItem value="hr">HR</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Component Category Dropdown */}
              <FormField
                control={form.control}
                name="component_category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Component Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || undefined}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select component category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {componentCategories.map((cat) => (
                          <SelectItem key={cat.value} value={cat.value}>
                            {cat.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="ai, automation, productivity (comma-separated)" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Environment Variables Section */}
              {activeTab === "github" && (
                <div className="space-y-4">
                  <h4 className="text-base font-medium">Environment Variables (Optional)</h4>
                  {envKeyFields.map((field, index) => (
                    <div key={field.id} className="flex gap-2 items-start">
                      <FormField
                        control={form.control}
                        name={`env_keys.${index}.key`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Input
                                placeholder="ENV_KEY_NAME"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`env_keys.${index}.description`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Input
                                placeholder="Description (optional)"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeEnvKey(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button type="button" variant="outline" size="sm" onClick={() => appendEnvKey({ key: "", description: "" })} className="w-fit">
                    <Plus className="h-4 w-4 mr-1" />
                    Add Variable
                  </Button>
                </div>
              )}

              {/* MCP Type for GitHub tab */}
              {activeTab === "github" && (
                <FormField
                  control={form.control}
                  name="mcp_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>MCP Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select MCP type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="SSE" disabled>
                            SSE (Deprecated)
                          </SelectItem>
                          <SelectItem value="Streamable HTTP">Streamable HTTP</SelectItem>
                          <SelectItem value="STDIO">STDIO</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Logo Upload */}
              <div className="space-y-3">
                <FormLabel>Logo (Optional)</FormLabel>
                <div className="space-y-3">
                  {logoUrl && (
                    <div className="flex items-center gap-4 p-4 border rounded-lg bg-muted/30">
                      <img src={logoUrl} alt="Current Logo" className="h-16 w-16 object-contain border rounded-md bg-white" />
                      <div className="flex-1">
                        <div className="text-sm font-medium">Current Logo</div>
                        <div className="text-xs text-muted-foreground">PNG, JPG formats supported</div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleRemoveLogo}
                        className="text-destructive hover:text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  <div className="space-y-2">
                    <FileUpload
                      onUploadSuccess={handleLogoUpload}
                      gcsPathPrefix="mcp-logos/"
                      acceptedFileTypes="image/png,image/jpeg,image/jpg,.png,.jpg,.jpeg"
                      className="min-h-[120px]"
                      customText={{
                        title: logoUrl ? "Replace Logo" : "Upload Logo",
                        subtitle: "Drag and drop or click to upload PNG, JPG files"
                      }}
                    />
                    <p className="text-xs text-muted-foreground text-center">
                      {logoUrl ? "Upload a new logo to replace the current one" : "Upload a logo for your MCP server (PNG, JPG formats only)"}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Current Repository/URLs Display */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Current Configuration</h3>

              {/* Show current GitHub repository if git_branch exists */}
              {activeTab === "github" && mcpServer.git_url && mcpServer.git_branch && (
                <div className="p-4 border rounded-lg bg-muted/30">
                  <div className="flex items-center gap-2 mb-3">
                    <Github className="h-5 w-5" />
                    <span className="font-medium">Current GitHub Repository</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FolderGit2 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-mono">{mcpServer.git_url}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <GitBranch className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Branch: {mcpServer.git_branch}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Show current URLs if git_branch is null/empty */}
              {activeTab === "hosted" && mcpServer.config && mcpServer.config.length > 0 && (
                <div className="p-4 border rounded-lg bg-muted/30">
                  <div className="flex items-center gap-2 mb-3">
                    <Globe className="h-5 w-5" />
                    <span className="font-medium">Current URLs</span>
                  </div>
                  <div className="space-y-2">
                    {mcpServer.config.map((url, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="w-26 text-xs bg-muted px-2 py-1 rounded-md">
                          {url.type.toUpperCase()}
                        </div>
                        <span className="text-sm font-mono">{url.url}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === "hosted" && (
                <div className="space-y-4">
                  <h4 className="text-base font-medium">Server URLs</h4>
                  {form.watch("serverConfigs")?.map((serverConfig, index) => (
                    <div key={index} className="flex gap-2 items-start">
                      <FormField
                        control={form.control}
                        name={`serverConfigs.${index}.url`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Input
                                placeholder="https://your-mcp-server.com"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`serverConfigs.${index}.protocol`}
                        render={({ field }) => (
                          <FormItem className="w-auto min-w-[150px]">
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="SSE">SSE</SelectItem>
                                <SelectItem value="Streamable HTTP">Streamable HTTP</SelectItem>
                                <SelectItem value="STDIO"> STDIO</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Git URL for Hosted URLs only */}
            {activeTab === "hosted" && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Additional Information</h3>

                <FormField
                  control={form.control}
                  name="gitUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Git URL (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://github.com/user/repo (optional)"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={updateMCPServer.isPending}
                className="gap-2"
              >
                {updateMCPServer.isPending ? (
                  <>
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Edit className="h-4 w-4" />
                    Update Server
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}